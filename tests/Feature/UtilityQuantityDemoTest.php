<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use App\Models\Utility;
use App\Services\ReservationCostService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Services\ReservationCostService::class)]
class UtilityQuantityDemoTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function demonstrates_new_utility_quantity_logic()
    {
        // Setup
        $user = User::factory()->create();
        $field = Field::factory()->create([
            'name' => 'Soccer Field A',
            'hourly_rate' => 50.00,
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8,
        ]);

        $projector = Utility::factory()->create([
            'name' => 'Projector',
            'hourly_rate' => 15.00,
            'is_active' => true,
        ]);

        $table = Utility::factory()->create([
            'name' => 'Table',
            'hourly_rate' => 5.00,
            'is_active' => true,
        ]);

        $costService = new ReservationCostService;

        // Test Case: Book field for 1.5 hours with 2 projectors and 3 tables
        $utilities = [
            ['id' => $projector->id, 'hours' => 2], // 2 projectors (quantity)
            ['id' => $table->id, 'hours' => 3],     // 3 tables (quantity)
        ];

        $result = $costService->calculateTotalCostWithUtilities(
            $field,
            1.5, // 1.5 hours field duration
            '10:00',
            $utilities
        );

        // Expected calculations:
        // Field cost: $50.00 × 1.5 hours = $75.00
        // Projector cost: $15.00 × 2 quantity = $30.00 (quantity only, not multiplied by duration)
        // Table cost: $5.00 × 3 quantity = $15.00 (quantity only, not multiplied by duration)
        // Total utility cost: $30.00 + $15.00 = $45.00
        // Total cost: $75.00 + $45.00 = $120.00

        $this->assertEquals(75.00, $result['field_cost']);
        $this->assertEquals(45.00, $result['utility_cost']);
        $this->assertEquals(120.00, $result['total_cost']);

        // Verify utility breakdown
        $this->assertCount(2, $result['utility_breakdown']);

        $projectorBreakdown = collect($result['utility_breakdown'])->firstWhere('utility_id', $projector->id);
        $this->assertEquals(2, $projectorBreakdown['hours']); // quantity
        $this->assertEquals(30.00, $projectorBreakdown['cost']);

        $tableBreakdown = collect($result['utility_breakdown'])->firstWhere('utility_id', $table->id);
        $this->assertEquals(3, $tableBreakdown['hours']); // quantity
        $this->assertEquals(15.00, $tableBreakdown['cost']);

        // Test validation: decimal quantities should be rejected
        $this->actingAs($user);

        $response = $this->post('/reservations', [
            'field_id' => $field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 1.5, // Field duration can be decimal
            'customer_name' => 'Test Customer',
            'utilities' => [
                [
                    'id' => $projector->id,
                    'hours' => 1.5, // This should be rejected (decimal quantity)
                ],
            ],
        ]);

        $response->assertSessionHasErrors('utilities.0.hours');

        // Test validation: whole number quantities should be accepted
        $response = $this->post('/reservations', [
            'field_id' => $field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 1.5, // Field duration can be decimal
            'customer_name' => 'Test Customer',
            'utilities' => [
                [
                    'id' => $projector->id,
                    'hours' => 2, // This should be accepted (whole number quantity)
                ],
            ],
        ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();
    }
}
