<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use App\Models\Utility;
use App\Services\ReservationCostService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Services\ReservationCostService::class)]
class UtilityCostCalculationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Field $field;

    private Utility $utility;

    private ReservationCostService $costService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8,
        ]);
        $this->utility = Utility::factory()->create([
            'name' => 'Test Projector',
            'hourly_rate' => 15.00,
            'is_active' => true,
        ]);
        $this->costService = new ReservationCostService;
    }

    #[Test]
    public function utility_cost_calculation_uses_quantity_only()
    {
        // Test: 2 projectors for 1.5 hours
        // Expected: 2 × $15.00 = $30.00 (quantity only, not multiplied by duration)
        $utilities = [
            ['id' => $this->utility->id, 'hours' => 2], // 2 quantity
        ];

        $result = $this->costService->calculateTotalCostWithUtilities(
            $this->field,
            1.5, // 1.5 hours field duration
            '10:00',
            $utilities
        );

        $this->assertEquals(75.00, $result['field_cost']); // 50 × 1.5
        $this->assertEquals(30.00, $result['utility_cost']); // 15 × 2 (quantity only)
        $this->assertEquals(105.00, $result['total_cost']); // 75 + 30

        // Check utility breakdown
        $this->assertCount(1, $result['utility_breakdown']);
        $this->assertEquals(2, $result['utility_breakdown'][0]['hours']); // quantity
        $this->assertEquals(30.00, $result['utility_breakdown'][0]['cost']);
    }

    #[Test]
    public function ajax_cost_estimate_works_with_new_utility_logic()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/reservations/cost-estimate', [
            'field_id' => $this->field->id,
            'duration_hours' => 2.0,
            'start_time' => '14:00',
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 3], // 3 quantity
            ],
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertEquals(100.00, $data['field_cost']); // 50 × 2
        $this->assertEquals(45.00, $data['utility_cost']); // 15 × 3 (quantity only)
        $this->assertEquals(145.00, $data['total_cost']); // 100 + 45
    }

    #[Test]
    public function multiple_utilities_calculate_correctly()
    {
        $utility2 = Utility::factory()->create([
            'name' => 'Test Table',
            'hourly_rate' => 5.00,
            'is_active' => true,
        ]);

        // Test: 2 projectors + 4 tables for 3 hours
        $utilities = [
            ['id' => $this->utility->id, 'hours' => 2], // 2 projectors
            ['id' => $utility2->id, 'hours' => 4], // 4 tables
        ];

        $result = $this->costService->calculateTotalCostWithUtilities(
            $this->field,
            3.0, // 3 hours
            '10:00',
            $utilities
        );

        $expectedFieldCost = 150.00; // 50 × 3
        $expectedProjectorCost = 30.00; // 15 × 2 (quantity only)
        $expectedTableCost = 20.00; // 5 × 4 (quantity only)
        $expectedUtilityCost = 50.00; // 30 + 20
        $expectedTotalCost = 200.00; // 150 + 50

        $this->assertEquals($expectedFieldCost, $result['field_cost']);
        $this->assertEquals($expectedUtilityCost, $result['utility_cost']);
        $this->assertEquals($expectedTotalCost, $result['total_cost']);

        // Check utility breakdown
        $this->assertCount(2, $result['utility_breakdown']);
    }

    #[Test]
    public function zero_utilities_works_correctly()
    {
        $result = $this->costService->calculateTotalCostWithUtilities(
            $this->field,
            2.5,
            '10:00',
            [] // No utilities
        );

        $this->assertEquals(125.00, $result['field_cost']); // 50 × 2.5
        $this->assertEquals(0.00, $result['utility_cost']);
        $this->assertEquals(125.00, $result['total_cost']);
        $this->assertEmpty($result['utility_breakdown']);
    }
}
